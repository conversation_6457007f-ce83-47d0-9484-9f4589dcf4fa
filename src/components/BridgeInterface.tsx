"use client";
import { useWallet, useConnection } from "@solana/wallet-adapter-react";
import { useWalletModal } from "@solana/wallet-adapter-react-ui";
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useConnect, useDisconnect } from "wagmi";
import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { useState, useEffect, useCallback, useMemo } from "react";
import { EndpointId } from "@layerzerolabs/lz-definitions";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { Connection, PublicKey } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { fromWeb3JsPublicKey, toWeb3JsTransaction } from '@metaplex-foundation/umi-web3js-adapters';
import { findAssociatedTokenPda, createSplAssociatedTokenProgram, setComputeUnitLimit, setComputeUnitPrice } from '@metaplex-foundation/mpl-toolbox';
import bs58 from 'bs58';
import { useReadContract } from 'wagmi';
import { erc20Abi, parseUnits } from 'viem';


const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");
const ETHEREUM_MAINNET_EID = EndpointId.ETHEREUM_V2_MAINNET;
const SOLANA_MAINNET_EID = EndpointId.SOLANA_V2_MAINNET;

const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};

const fetchGasPrice = async (): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> => {
  try {
    const response = await fetch('https://api.blocknative.com/gasprices/blockprices?chainid=1');
    const data = await response.json();

    if (data.blockPrices && data.blockPrices.length > 0) {
      const estimatedPrices = data.blockPrices[0].estimatedPrices;
      const price90 = estimatedPrices.find((p: { confidence: number; maxFeePerGas: number; maxPriorityFeePerGas: number }) => p.confidence === 90);

      if (price90) {
        const maxFeePerGas = BigInt(Math.ceil(price90.maxFeePerGas * 1e9));
        const maxPriorityFeePerGas = BigInt(Math.ceil(price90.maxPriorityFeePerGas * 1e9));

        return { maxFeePerGas, maxPriorityFeePerGas };
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
};

const ETHEREUM_GAS_LIMIT = BigInt(250000);

const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

const isValidSolanaAddress = (address: string): boolean => {
  try {
    const decoded = bs58.decode(address);
    return decoded.length === 32;
  } catch {
    return false;
  }
};

const oftAbi = [
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      { "internalType": "bool", "name": "_payInLzToken", "type": "bool" }
    ],
    "name": "quoteSend",
    "outputs": [
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "msgFee",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "_fee",
        "type": "tuple"
      },
      { "internalType": "address", "name": "_refundAddress", "type": "address" }
    ],
    "name": "send",
    "outputs": [
      {
        "components": [
          { "internalType": "bytes32", "name": "guid", "type": "bytes32" },
          { "internalType": "uint64", "name": "nonce", "type": "uint64" },
          {
            "components": [
              { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
              { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
            ],
            "internalType": "struct MessagingFee",
            "name": "fee",
            "type": "tuple"
          }
        ],
        "internalType": "struct MessagingReceipt",
        "name": "msgReceipt",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "amountSentLD", "type": "uint256" },
          { "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256" }
        ],
        "internalType": "struct OFTReceipt",
        "name": "oftReceipt",
        "type": "tuple"
      }
    ],
    "stateMutability": "payable",
    "type": "function"
  }
] as const;

interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
  gasPrice: { maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null;
  customEthAddress: string;
  customSolanaAddress: string;
}

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

export default function BridgeInterface() {
  const solanaWallet = useWallet();
  const { connection } = useConnection();
  const { setVisible: setSolanaWalletModalVisible } = useWalletModal();
  const { address: ethAddress, isConnected: isEthConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { connect, connectors, isPending: isConnectPending } = useConnect();

  const [isClient, setIsClient] = useState(false);
  const [isEthWalletModalOpen, setIsEthWalletModalOpen] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [direction, setDirection] = useState<'sol-to-eth' | 'eth-to-sol'>('sol-to-eth');
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
    gasPrice: null,
    customEthAddress: '',
    customSolanaAddress: '',
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isHistoryVisible, setIsHistoryVisible] = useState(false);

  const umi = useMemo(() => {
    const umiInstance = createUmi(SOLANA_RPC_URL);
    if (solanaWallet.wallet) {
      umiInstance.use(walletAdapterIdentity(solanaWallet));
    }
    umiInstance.programs.add(createSplAssociatedTokenProgram());
    return umiInstance;
  }, [solanaWallet]);

  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending, error: ethTxError } = useWriteContract();
  const { isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  const fetchSolanaBalance = useCallback(async () => {
    if (!solanaWallet.publicKey || !SOLANA_OFT_MINT_ADDRESS || !solanaWallet.connected) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(solanaWallet.publicKey.toString());

      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey, solanaWallet.connected]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  useEffect(() => {
    if (ethTxHash && isEthTxSuccess) {
      const layerZeroScanLink = getLayerZeroScanLink(ethTxHash, false);

      const newTransaction: Transaction = {
        hash: ethTxHash,
        timestamp: Date.now(),
        fromChain: 'ethereum',
        toChain: 'solana',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash: ethTxHash,
        layerZeroScanLink,
        isLoading: false
      }));
    }
  }, [ethTxHash, isEthTxSuccess, amount]);

  useEffect(() => {
    if (isEthTxPending) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }
  }, [isEthTxPending]);

  useEffect(() => {
    if (ethTxError) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: ethTxError.message || "Transaction failed"
      }));
    }
  }, [ethTxError]);

  // Add effect to reset button state when amount changes
  useEffect(() => {
    setBridgeState(prev => ({
      ...prev,
      nativeFee: null,
      txHash: null,
      layerZeroScanLink: null,
      error: null,
    }));
  }, [amount]);

  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
      gasPrice: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    if (direction === 'sol-to-eth') {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      if (!ethAddress && !bridgeState.customEthAddress) {
        throw new Error("Please connect your Ethereum wallet or enter a recipient address.");
      }
      if (bridgeState.customEthAddress && !isValidEthereumAddress(bridgeState.customEthAddress)) {
        throw new Error("Please enter a valid Ethereum address.");
      }
    } else {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      if (!solanaWallet.publicKey && !bridgeState.customSolanaAddress) {
        throw new Error("Please connect your Solana wallet or enter a recipient address.");
      }
      if (bridgeState.customSolanaAddress && !isValidSolanaAddress(bridgeState.customSolanaAddress)) {
        throw new Error("Please enter a valid Solana address.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }

    // Check if amount exceeds balance
    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      if (amountNum > solanaBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.solanaBalance || '0'} tokens.`);
      }
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      if (amountNum > ethereumBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.ethereumBalance || '0'} tokens.`);
      }
    }
  }, [direction, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount, bridgeState.customEthAddress, bridgeState.customSolanaAddress, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const quoteSolanaToEthereum = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));

      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey!),
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
        }
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Solana has 6 decimals, Ethereum has 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, ethAddress, solanaWallet.publicKey, umi, bridgeState.customEthAddress]);

  const executeSolanaToEthereum = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteSolanaToEthereum();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));
      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get token account
      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(SOLANA_OFT_MINT_ADDRESS!)),
        owner: publicKey(solanaWallet.publicKey!),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: bridgeState.nativeFee,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Add compute unit instructions to handle the LayerZero transaction
      const computeUnitLimitIx = setComputeUnitLimit(umi, { units: 400_000 });
      const computeUnitPriceIx = setComputeUnitPrice(umi, { microLamports: 1000 });

      const {
        context: { slot: minContextSlot },
        value: { blockhash, lastValidBlockHeight }
      } = await connection.getLatestBlockhashAndContext('finalized');

      const txB = transactionBuilder()
        .add(computeUnitLimitIx)
        .add(computeUnitPriceIx)
        .add([ix])
        .setBlockhash(blockhash);

      const umiTx = txB.build(umi);
      const web3Tx = toWeb3JsTransaction(umiTx);

      const signature = await solanaWallet.sendTransaction(web3Tx, connection, { minContextSlot });
      const txHash = signature;

      await connection.confirmTransaction({ blockhash, lastValidBlockHeight, signature });
      const layerZeroScanLink = getLayerZeroScanLink(txHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: txHash,
        timestamp: Date.now(),
        fromChain: 'solana',
        toChain: 'ethereum',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Refresh balance after successful transaction
      if (solanaWallet.connected && solanaWallet.publicKey) {
        const newBalance = await fetchSolanaBalance();
        setBridgeState(prev => ({ ...prev, solanaBalance: newBalance }));
      }
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteSolanaToEthereum, amount, ethAddress, solanaWallet.publicKey, umi, fetchSolanaBalance, solanaWallet.connected, bridgeState.customEthAddress]);

  const quoteEthereumToSolana = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Get current gas prices from Blocknative
      const gasPrice = await fetchGasPrice();

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = ETHEREUM_GAS_LIMIT * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = BigInt(20 * 1e9); // 20 gwei
        nativeFee = ETHEREUM_GAS_LIMIT * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Ethereum has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        gasPrice,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, solanaWallet.publicKey, bridgeState.customSolanaAddress]);

  const executeEthereumToSolana = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteEthereumToSolana();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Use custom address if provided, otherwise use connected wallet address
      const recipientSolanaAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey!.toString();
      const solanaAddressBytes = bs58.decode(recipientSolanaAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: SOLANA_MAINNET_EID,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: bridgeState.nativeFee,
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const gasPrice = await fetchGasPrice();

      // Execute the actual contract transaction with optimized gas
      writeOftContract({
        address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: bridgeState.nativeFee,
        gas: ETHEREUM_GAS_LIMIT,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      // The transaction hash will be available in ethTxHash after the transaction is submitted
      // We'll handle the success case in a useEffect that watches for ethTxHash changes

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteEthereumToSolana, amount, solanaWallet.publicKey, ethAddress, writeOftContract, bridgeState.customSolanaAddress]);

  const hasInsufficientBalance = useMemo(() => {
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) return false;

    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      return amountNum > solanaBalance;
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      return amountNum > ethereumBalance;
    }
  }, [direction, amount, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const canQuote = useMemo(() => {
    const basicRequirements = direction === 'sol-to-eth'
      ? solanaWallet.connected && (ethAddress || bridgeState.customEthAddress)
      : isEthConnected && (solanaWallet.publicKey || bridgeState.customSolanaAddress);

    return basicRequirements && !hasInsufficientBalance;
  }, [direction, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey, bridgeState.customEthAddress, bridgeState.customSolanaAddress, hasInsufficientBalance]);

  const clearTransactionHistory = useCallback(() => {
    setTransactions([]);
  }, []);

  if (!isClient) return null;

  return (
    <div className="w-full">
      {/* Main Bridge Interface */}
      <div className="relative pt-8 md:pt-40">
        {/* Bridge Image */}
        <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-[95%] md:-translate-y-[98%]">
          <img
            src="/bridge.png"
            alt="Bridge"
            className="w-56 md:w-80 lg:w-96 h-auto object-contain"
          />
        </div>

        {/* Bridge Interface */}
        <div className="bg-chillhouse-ui/50 backdrop-blur-md rounded-2xl p-3 md:p-8 shadow-2xl border border-chillhouse-ui/60 flex flex-col md:flex-row gap-3 md:gap-6 max-w-5xl mx-auto">
          {/* From Token Section */}
          <div className="flex-1 min-w-0">
            <div className="bg-chillhouse-ui/60 backdrop-blur-sm rounded-xl p-3 md:p-4 border border-chillhouse-ui/80">
              <div className="flex justify-between items-center mb-3">
                <span className="text-black text-sm font-medium">From</span>
                <span className="text-black text-sm font-medium">
                  Balance: {direction === 'sol-to-eth' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                </span>
              </div>
              <div className="flex items-center justify-between gap-3">
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.0"
                  className="bg-transparent text-black text-2xl font-semibold outline-none flex-1 min-w-0 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  step="0.000001"
                  min="0"
                />
                <div className="flex items-center space-x-2 bg-chillhouse-ui/80 rounded-lg px-3 py-2 flex-shrink-0 shadow-sm">
                  <img
                    src={direction === 'sol-to-eth' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                    alt={direction === 'sol-to-eth' ? 'Solana' : 'Ethereum'}
                    className="w-5 h-5"
                  />
                  <img
                    src="/token.webp"
                    alt="CHILLHOUSE Token"
                    className="w-5 h-5"
                  />
                  <span className="text-gray-900 font-semibold text-sm">CHILLHOUSE</span>
                </div>
              </div>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex items-center justify-center">
            <button
              onClick={() => {
                setDirection(direction === 'sol-to-eth' ? 'eth-to-sol' : 'sol-to-eth');
                resetBridgeState();
              }}
              className="bg-chillhouse-ui/60 hover:bg-chillhouse-ui rounded-full p-3 transition-colors border border-chillhouse-ui"
            >
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m-4 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </button>
          </div>

          {/* To Token Section */}
          <div className="flex-1 min-w-0">
            <div className="bg-chillhouse-ui/60 backdrop-blur-sm rounded-xl p-3 md:p-4 border border-chillhouse-ui/80">
              <div className="flex justify-between items-center mb-3">
                <span className="text-black text-sm font-medium">To</span>
                <span className="text-black text-sm font-medium">
                  Balance: {direction === 'eth-to-sol' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                </span>
              </div>
              <div className="flex items-center justify-between gap-3">
                <div className="text-black text-2xl font-semibold flex-1 min-w-0 overflow-hidden text-ellipsis">
                  {bridgeState.receiveAmount || '0.0'}
                </div>
                <div className="flex items-center space-x-2 bg-chillhouse-ui/80 rounded-lg px-3 py-2 flex-shrink-0 shadow-sm">
                  <img
                    src={direction === 'eth-to-sol' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                    alt={direction === 'eth-to-sol' ? 'Solana' : 'Ethereum'}
                    className="w-5 h-5"
                  />
                  <img
                    src="/token.webp"
                    alt="CHILLHOUSE Token"
                    className="w-5 h-5"
                  />
                  <span className="text-gray-900 font-semibold text-sm">CHILLHOUSE</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Connection and Action Section */}
        <div className="mt-4 md:mt-6 max-w-5xl mx-auto space-y-4 md:space-y-6">
          {/* Wallet Connection Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
            {/* Solana Wallet */}
            <div className="flex items-center justify-between p-3 bg-chillhouse-ui/60 backdrop-blur-sm rounded-lg border border-chillhouse-ui/80 shadow-sm">
              <div className="flex items-center space-x-3">
                <img src="/solana-sol-logo.svg" alt="Solana" className="w-6 h-6" />
                <span className="text-black font-semibold">Solana</span>
              </div>
              {solanaWallet.connected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-600 text-sm font-medium">
                    {solanaWallet.publicKey?.toString().slice(0, 4)}...{solanaWallet.publicKey?.toString().slice(-4)}
                  </span>
                  <button
                    onClick={() => solanaWallet.disconnect()}
                    className="bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 backdrop-blur-sm text-black hover:text-gray-700 text-sm font-medium px-3 py-1 rounded-lg border border-chillhouse-ui/80 transition-colors"
                  >
                    Disconnect
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setSolanaWalletModalVisible(true)}
                  className="bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 backdrop-blur-sm text-black text-sm font-medium py-2 px-4 rounded-lg border border-chillhouse-ui/80 transition-colors"
                >
                  Connect Wallet
                </button>
              )}
            </div>

            {/* Ethereum Wallet */}
            <div className="flex items-center justify-between p-3 bg-chillhouse-ui/60 backdrop-blur-sm rounded-lg border border-chillhouse-ui/80 shadow-sm">
              <div className="flex items-center space-x-3">
                <img src="/ethereum-eth-logo.svg" alt="Ethereum" className="w-6 h-6" />
                <span className="text-black font-semibold">Ethereum</span>
              </div>
              {isEthConnected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-600 text-sm font-medium">
                    {ethAddress?.slice(0, 6)}...{ethAddress?.slice(-4)}
                  </span>
                  <button
                    onClick={() => disconnect()}
                    className="bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 backdrop-blur-sm text-black hover:text-gray-700 text-sm font-medium px-3 py-1 rounded-lg border border-chillhouse-ui/80 transition-colors"
                  >
                    Disconnect
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setIsEthWalletModalOpen(true)}
                  className="bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 backdrop-blur-sm text-black text-sm font-medium py-2 px-4 rounded-lg border border-chillhouse-ui/80 transition-colors"
                >
                  Connect Wallet
                </button>
              )}
            </div>
          </div>

          {/* Custom Address Inputs */}
          {direction === 'sol-to-eth' && !ethAddress && (
            <div>
              <input
                type="text"
                value={bridgeState.customEthAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customEthAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-chillhouse-ui/60 backdrop-blur-sm border border-chillhouse-ui/80 rounded-lg text-gray-900 placeholder-gray-600 focus:outline-none focus:border-chillhouse-ui shadow-sm font-medium"
                placeholder="Enter Ethereum address (0x...)"
              />
            </div>
          )}

          {direction === 'eth-to-sol' && !solanaWallet.publicKey && (
            <div>
              <input
                type="text"
                value={bridgeState.customSolanaAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customSolanaAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-chillhouse-ui/60 backdrop-blur-sm border border-chillhouse-ui/80 rounded-lg text-gray-900 placeholder-gray-600 focus:outline-none focus:border-chillhouse-ui shadow-sm font-medium"
                placeholder="Enter Solana address..."
              />
            </div>
          )}

          {/* Error Display */}
          {bridgeState.error && (
            <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md p-4 bg-red-900/80 border border-red-500/30 rounded-lg">
              <p className="text-sm text-red-400">{bridgeState.error}</p>
            </div>
          )}

          {/* Action Button */}
          <div>
            {direction === 'sol-to-eth' ? (
              <button
                onClick={async () => {
                  if (!bridgeState.nativeFee) {
                    await quoteSolanaToEthereum();
                  } else {
                    await executeSolanaToEthereum();
                  }
                }}
                disabled={!canQuote || bridgeState.isLoading || !!bridgeState.txHash}
                className="w-full py-4 px-6 bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 disabled:from-gray-600/50 disabled:to-gray-600/50 disabled:cursor-not-allowed text-black font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center backdrop-blur-md border border-chillhouse-ui/80 min-h-[64px]"
              >
                {bridgeState.isLoading ? (
                  <>
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{bridgeState.nativeFee ? 'Sending...' : 'Getting Quote...'}</span>
                    </div>
                  </>
                ) : bridgeState.txHash ? (
                  <div className="flex items-center gap-3">
                    <span>Tokens bridged!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-300 hover:text-blue-200"
                        onClick={(e) => e.stopPropagation()}
                      >
                        View transaction ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex items-center gap-3">
                    <span>Bridge Tokens</span>
                    <span className="text-sm text-gray-300">
                      {(Number(bridgeState.nativeFee) / 1e9).toFixed(6)} SOL Fee
                    </span>
                  </div>
                ) : (
                  <span>Get Quote & Bridge</span>
                )}
              </button>
            ) : (
              <button
                onClick={async () => {
                  if (!bridgeState.nativeFee) {
                    await quoteEthereumToSolana();
                  } else {
                    await executeEthereumToSolana();
                  }
                }}
                disabled={!canQuote || bridgeState.isLoading || !!bridgeState.txHash}
                className="w-full py-4 px-6 bg-chillhouse-ui/60 hover:bg-chillhouse-ui/80 disabled:from-gray-600/50 disabled:to-gray-600/50 disabled:cursor-not-allowed text-black font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center backdrop-blur-md border border-chillhouse-ui/80 min-h-[64px]"
              >
                {bridgeState.isLoading ? (
                  <>
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{bridgeState.nativeFee ? 'Sending...' : 'Getting Quote...'}</span>
                    </div>
                  </>
                ) : bridgeState.txHash ? (
                  <div className="flex items-center gap-3">
                    <span>Tokens bridged!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-300 hover:text-blue-200"
                        onClick={(e) => e.stopPropagation()}
                      >
                        View transaction ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex items-center gap-3">
                    <span>Bridge Tokens</span>
                    <span className="text-sm text-gray-300">
                      {(Number(bridgeState.nativeFee) / 1e18).toFixed(6)} ETH Fee
                    </span>
                  </div>
                ) : (
                  <span>Get Quote & Bridge</span>
                )}
              </button>
            )}
          </div>

          {/* Transaction History Button & Panel */}
          {transactions.length > 0 && (
            <div className="mt-4 md:mt-8 text-center">
              <button
                onClick={() => setIsHistoryVisible(!isHistoryVisible)}
                className="text-sm text-chillhouse-ui hover:text-chillhouse-ui/80 flex items-center gap-2 mx-auto"
              >
                <svg
                  className={`w-4 h-4 transition-transform ${isHistoryVisible ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
                {isHistoryVisible ? 'Hide' : 'View'} Transaction History ({transactions.length})
              </button>

              {isHistoryVisible && (
                <div className="mt-3 md:mt-4 max-w-3xl mx-auto bg-chillhouse-ui/60 backdrop-blur-sm rounded-xl p-3 md:p-4 border border-chillhouse-ui/80 text-left space-y-2 md:space-y-3 shadow-sm">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-white font-semibold">Transaction History</h4>
                    <button
                      onClick={clearTransactionHistory}
                      className="text-xs text-red-400 hover:text-red-300"
                    >
                      Clear All
                    </button>
                  </div>

                  {transactions.map((tx, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-chillhouse-ui/70 rounded-lg border border-chillhouse-ui/80 shadow-sm"
                    >
                      <div className="flex items-center gap-3">
                        <img
                          src={tx.fromChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.fromChain}
                          className="w-5 h-5"
                        />
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                        <img
                          src={tx.toChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.toChain}
                          className="w-5 h-5"
                        />
                        <span className="text-black text-sm font-medium">{tx.amount}</span>
                      </div>
                      <a
                        href={tx.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                      >
                        <span>LayerZero Scan</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Powered by LayerZero */}
          <div className="flex items-center justify-center pt-2 md:pt-4">
            <div className="flex items-center space-x-2 bg-chillhouse-ui/60 backdrop-blur-md rounded-lg px-4 py-2 border border-chillhouse-ui/80">
              <span className="text-black text-sm font-medium">Powered by</span>
              <img
                src="/layerzero.svg"
                alt="LayerZero"
                className="h-5 md:h-6 opacity-80"
              />
            </div>
          </div>
        </div>
      </div>

      {isEthWalletModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]">
          <div className="bg-gray-900 rounded-2xl p-6 max-w-sm w-full mx-4 border border-gray-700 relative">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Connect Ethereum Wallet</h3>
              <button
                onClick={() => setIsEthWalletModalOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {connectors.map((connector) => (
                <button
                  key={connector.uid}
                  onClick={() => {
                    connect({ connector });
                    setIsEthWalletModalOpen(false);
                  }}
                  disabled={isConnectPending}
                  className="w-full flex items-center space-x-3 p-3 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors disabled:opacity-50"
                >
                  <div className="w-8 h-8 bg-chillhouse-ui rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {connector.name.charAt(0)}
                    </span>
                  </div>
                  <span className="text-white font-medium">{connector.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
